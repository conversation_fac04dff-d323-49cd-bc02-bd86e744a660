package com.stt.android.device.remote.watchface

import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.domain.watchface.WatchFaceItem
import com.stt.android.device.domain.watchface.WatchFaceListContainer
import javax.inject.Inject

class WatchFaceRemoteDataSource @Inject constructor(
    private val onlineWatchFaceRemoteAPI: OnlineWatchFaceRemoteAPI,
) {
    suspend fun fetchAll(
        variantName: String,
        watchCapabilities: String
    ): WatchFaceListContainer {
        return onlineWatchFaceRemoteAPI.fetchAll(variantName, watchCapabilities).toDomain()
    }

    suspend fun getWatchFaceDetails(
        variantName: String,
        watchCapabilities: String
    ): WatchFaceEntity {
        return onlineWatchFaceRemoteAPI.getWatchFaceDetails(variantName, watchCapabilities)
            .toDomain()
    }

    suspend fun fetchWatchFaceFile(
        runFeatureCatalogueId: String,
        watchCapabilities: String,
    ): ByteArray {
        return onlineWatchFaceRemoteAPI.fetchWatchFaceFile(runFeatureCatalogueId, watchCapabilities)
    }

    suspend fun updateEnabledState(runFeatureCatalogueId: String, enabled: Boolean) {
        // todo:
    }

    suspend fun addToLibrary(
        runFeatureCatalogueId: String,
        watchCapabilities: String,
        addToLibrary: Boolean,
        addToWatch: Boolean,
    ): Boolean {
        return onlineWatchFaceRemoteAPI.addToLibrary(
            runFeatureCatalogueId,
            watchCapabilities,
            addToLibrary = addToLibrary,
            addToWatch = addToWatch
        )
    }

    suspend fun fetchUserLibrary(
        watchCapabilities: String
    ): List<WatchFaceEntity> {
        return onlineWatchFaceRemoteAPI.fetchUserLibrary(watchCapabilities)
    }
}
