package com.stt.android.device.remote.watchface

import com.squareup.moshi.JsonClass
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.domain.watchface.WatchFaceListContainer
import com.stt.android.device.domain.watchface.WatchFaceItem

interface OnlineWatchFaceRemoteAPI {

    suspend fun fetchAll(
        variantName: String,
        watchCapabilities: String
    ): RemoteWatchFaceListContainer

    suspend fun getWatchFaceDetails(
        runFeatureCatalogueId: String,
        watchCapabilities: String,
    ): RemoteWatchFaceEntity

    suspend fun fetchWatchFaceFile(
        runFeatureCatalogueId: String,
        watchCapabilities: String,
    ): ByteArray

    suspend fun addToLibrary(
        runFeatureCatalogueId: String,
        watchCapabilities: String,
        addToLibrary: Boolean,
        addToWatch: Boolean
    ): Boolean

    suspend fun fetchUserLibrary(
        watchCapabilities: String
    ): RemoteUserLibraryWatchFaces
}

@JsonClass(generateAdapter = true)
data class RemoteWatchFaceListContainer(
    val id: String,
    val title: String,
    val description: String?,
    val bannerImageUrl: String?,
    val runFeaturesCatalogues: List<RemoteWatchFaceItem>,
)

@JsonClass(generateAdapter = true)
data class RemoteWatchFaceItem(
    val id: String,
    val type: String,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val currentVersion: String,
    val watchCapability: String,
    val watchfaceId: String,
    val richText: String?,
    val labels: List<String>?,
    val iconUrl: String?,
    val supported: Boolean,
    val inUserLibrary: Boolean,
    val useDefaultImages: Boolean,
)

@JsonClass(generateAdapter = true)
data class RemoteWatchFaceEntity(
    val runFeatureCatalogueId: String,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val labels: List<String>?,
    val watchfaceId: String,
    val richText: String?,
    val iconUrl: String?,
    val tileBannerUrl: String?,
    val supported: Boolean,
    val updated: Boolean,
    val addToWatch: Boolean,
    val addToFavorite: Boolean,
    val type: String,
    val fileSize: Long,
    val md5: String,
    val latestInstalledVersion: String?,
    val targetInstallVersion: String,
    val latestInstalledCapability: String?,
    val targetInstallCapability: String
)

@JsonClass(generateAdapter = true)
data class RemoteUserLibraryWatchFaces(
    val installSupportedRunFeatures: List<RemoteWatchFaceItem>?,
    val installNotSupportedRunFeatures: List<RemoteWatchFaceItem>?,
    val notInstalledRunFeatures: List<RemoteWatchFaceItem>?,
)

fun RemoteWatchFaceListContainer.toDomain() = WatchFaceListContainer(
    id = id,
    title = title,
    description = description,
    bannerImageUrl = bannerImageUrl,
    itemList = runFeaturesCatalogues.map { it.toDomain() }
)

fun RemoteWatchFaceItem.toDomain() = WatchFaceItem(
    id = id,
    type = type,
    name = name,
    description = description,
    shortDescription = shortDescription,
    currentVersion = currentVersion,
    watchCapability = watchCapability,
    watchfaceId = watchfaceId,
    richText = richText,
    labels = labels,
    iconUrl = iconUrl,
    supported = supported,
    inUserLibrary = inUserLibrary,
    useDefaultImages = useDefaultImages,
)

fun RemoteWatchFaceEntity.toDomain() = WatchFaceEntity(
    runFeatureCatalogueId = runFeatureCatalogueId,
    name = name,
    description = description,
    shortDescription = shortDescription,
    labels = labels,
    watchfaceId = watchfaceId,
    richText = richText,
    iconUrl = iconUrl,
    tileBannerUrl = tileBannerUrl,
    supported = supported,
    updated = updated,
    addToWatch = addToWatch,
    addToFavorite = addToFavorite,
    type = type,
    fileSize = fileSize,
    md5 = md5,
    latestInstalledVersion = latestInstalledVersion,
    targetInstallVersion = targetInstallVersion,
    latestInstalledCapability = latestInstalledCapability,
    targetInstallCapability = targetInstallCapability,
)

fun RemoteUserLibraryWatchFaces.toDomain() =
    installSupportedRunFeatures?.map { it.toDomain() }.orEmpty() +
        installNotSupportedRunFeatures?.map { it.toDomain() }.orEmpty() +
        notInstalledRunFeatures?.map { it.toDomain() }.orEmpty()


